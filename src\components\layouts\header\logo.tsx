import { Link } from 'react-router-dom';

type LogoLinkProps = {
  companyLogo: string;
};

export default function LogoLink({ companyLogo }: LogoLinkProps) {
  return (
    <div className="relative z-10 max-w-[180px] m-auto">
      <Link to="/app/home">
        <img
          className="w-full max-h-[75px] py-4 px-0"
          alt="logo da empresa"
          src={companyLogo}
        />
      </Link>
    </div>
  );
}
