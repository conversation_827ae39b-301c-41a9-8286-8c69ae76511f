import { ModalCompanyForm } from '@/pages/App/pages/CustomerCompanies/modalForm';
import { ClientCompany } from '@/services/api/clients/remoteLoadClientCompanies';
import { format } from 'date-fns';
import { Badge } from '../ui/badge';
import { ColumnDef, DataTable } from '../ui/data-table';

type CustomerCompaniesTableProps = {
  data: ClientCompany[];
  loading?: boolean;
};

export function CustomerCompaniesTable({
  data,
  loading,
}: CustomerCompaniesTableProps) {
  const columns: ColumnDef<ClientCompany>[] = [
    {
      id: 'name',
      header: 'Nome',
      accessorKey: 'name',
      sortable: true,
      searchable: true,
      className: '!text-[16px]',
      width: 400,
      maxWidth: 400,
      cell: ({ value }) => <span className="line-clamp-1">{value}</span>,
    },
    {
      id: 'isActive',
      header: 'Ativo',
      accessorKey: 'isActive',
      sortable: true,
      searchable: true,
      cell: ({ value }) => (
        <Badge variant={value ? 'default' : 'destructive'}>
          {value ? 'Sim' : 'Não'}
        </Badge>
      ),
    },
    {
      id: 'includedBenSaude',
      header: 'Incluso Ben + Saúde',
      accessorKey: 'includedBenSaude',
      sortable: true,
      searchable: true,
      cell: ({ value }) => (
        <Badge variant={value ? 'default' : 'destructive'}>
          {value ? 'Sim' : 'Não'}
        </Badge>
      ),
    },
    {
      id: 'createdAt',
      header: 'Data de cadastro',
      accessorKey: 'createdAt',
      sortable: true,
      cell: ({ value }) => format(new Date(value), 'dd/MM/yyyy/HH:mm'),
    },
    {
      id: 'actions',
      header: <ModalCompanyForm />,
      sortable: false,
      width: 80,
      align: 'right',
      cell: ({ row }) => (
        <ModalCompanyForm
          isEdit
          defaultValues={row}
        />
      ),
    },
  ];

  return (
    <DataTable
      className="w-full"
      searchPlaceholder="Pesquise pelo nome da empresa..."
      columns={columns}
      data={data}
      loading={loading}
      initialPageSize={10}
      pageSizeOptions={[10, 25, 50]}
      searchDebounceMs={300}
      enableSearch
      enableSorting
      enablePagination
    />
  );
}
