import { ModalUnityForm } from '@/pages/App/pages/CustomerCompanies/unityModal';
import { ClientUnity } from '@/services/api/clients/remoteLoadAllUnities';
import { Badge } from '../ui/badge';
import { ColumnDef, DataTable } from '../ui/data-table';
import { formatarCNPJ } from '@/utils/validations/cnpj';

type CustomerCompaniesTableProps = {
  data: ClientUnity[];
  loading?: boolean;
};

export function UnityCompanyTable({
  data,
  loading,
}: CustomerCompaniesTableProps) {
  const columns: ColumnDef<ClientUnity>[] = [
    {
      id: 'nome',
      header: 'Nome',
      accessorKey: 'nome',
      sortable: true,
      searchable: true,
      className: '!text-[16px]',
      width: 280,
      maxWidth: 280,
      cell: ({ value }) => <span className="line-clamp-1">{value}</span>,
    },
    {
      id: 'nomeEmpresaCliente',
      header: 'Empresa',
      accessorKey: 'nomeEmpresaCliente',
      sortable: true,
      searchable: true,
    },
    {
      id: 'cnpj',
      header: 'CNPJ',
      accessorKey: 'cnpj',
      sortable: true,
      searchable: true,
      cell: ({ value }) => formatarCNPJ(value),
    },
    {
      id: 'idEmpresaSoc',
      header: 'Código SOC',
      accessorKey: 'idEmpresaSoc',
      sortable: true,
    },
    {
      id: 'tipoEstabelecimento',
      header: 'Matriz/Filial',
      accessorKey: 'tipoEstabelecimento',
      sortable: true,
      cell: ({ value }) => <span className="capitalize">{value}</span>,
    },
    {
      id: 'status',
      header: 'Ativo',
      accessorKey: 'status',
      sortable: true,
      searchable: true,
      cell: ({ value }) => (
        <Badge variant={value ? 'default' : 'destructive'}>
          {value ? 'Sim' : 'Não'}
        </Badge>
      ),
    },
    {
      id: 'actions',
      header: <ModalUnityForm />,
      sortable: false,
      width: 80,
      align: 'right',
      cell: ({ row }) => (
        <ModalUnityForm
          isEdit
          defaultValues={row}
        />
      ),
    },
  ];

  return (
    <DataTable
      className="w-full"
      searchPlaceholder="Pesquise pelo nome da empresa..."
      columns={columns}
      data={data}
      loading={loading}
      initialPageSize={10}
      pageSizeOptions={[10, 20, 30]}
      searchDebounceMs={300}
      enableSearch
      enableSorting
      enablePagination
    />
  );
}
