/* eslint-disable react-refresh/only-export-components */
import { ArrowUpDown, ArrowU<PERSON>, ArrowDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { TableHead } from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { SortableHeaderProps, SortDirection } from './types';

export function SortableHeader({
  children,
  sortKey,
  currentSort,
  onSort,
  sortable = true,
  className,
  align = 'left',
}: SortableHeaderProps) {
  // Find current sort state for this column
  const currentSortConfig = currentSort.find((sort) => sort.key === sortKey);
  const sortDirection: SortDirection = currentSortConfig?.direction || null;

  const handleSort = () => {
    if (!sortable) return;
    onSort(sortKey);
  };

  const getSortIcon = () => {
    if (!sortable) return null;

    switch (sortDirection) {
      case 'asc':
        return <ArrowUp className="ml-2 h-4 w-4" />;
      case 'desc':
        return <ArrowDown className="ml-2 h-4 w-4" />;
      default:
        return <ArrowUpDown className="ml-2 h-4 w-4 opacity-50" />;
    }
  };

  const getAlignmentClass = () => {
    switch (align) {
      case 'center':
        return 'text-center justify-center';
      case 'right':
        return 'text-right justify-end';
      default:
        return 'text-left justify-start';
    }
  };

  if (!sortable) {
    return (
      <TableHead className={cn(getAlignmentClass(), className)}>
        <div className="flex items-center">{children}</div>
      </TableHead>
    );
  }

  return (
    <TableHead className={cn(getAlignmentClass(), className)}>
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          'h-auto p-0 font-medium hover:bg-transparent',
          'flex items-center',
          getAlignmentClass(),
          sortDirection && 'text-foreground',
        )}
        onClick={handleSort}
        aria-label={`Sort by ${sortKey} ${
          sortDirection === 'asc'
            ? '(currently ascending, click for descending)'
            : sortDirection === 'desc'
              ? '(currently descending, click for ascending)'
              : '(not sorted, click for ascending)'
        }`}
        aria-sort={
          sortDirection === 'asc'
            ? 'ascending'
            : sortDirection === 'desc'
              ? 'descending'
              : 'none'
        }
      >
        <span className="flex items-center">
          {children}
          {getSortIcon()}
        </span>
      </Button>
    </TableHead>
  );
}

// Utility function to get next sort direction
export function getNextSortDirection(current: SortDirection): SortDirection {
  switch (current) {
    case null:
      return 'asc';
    case 'asc':
      return 'desc';
    case 'desc':
      return null;
    default:
      return 'asc';
  }
}

// Utility function to update sort configuration
export function updateSortConfig(
  currentSort: Array<{ key: string; direction: SortDirection }>,
  sortKey: string,
  multiSort: boolean = false,
): Array<{ key: string; direction: SortDirection }> {
  const existingSort = currentSort.find((sort) => sort.key === sortKey);
  const nextDirection = getNextSortDirection(existingSort?.direction || null);

  if (!multiSort) {
    // Single column sorting - replace all sorts
    return nextDirection ? [{ key: sortKey, direction: nextDirection }] : [];
  }

  // Multi-column sorting
  if (nextDirection === null) {
    // Remove this sort
    return currentSort.filter((sort) => sort.key !== sortKey);
  }

  if (existingSort) {
    // Update existing sort
    return currentSort.map((sort) =>
      sort.key === sortKey ? { ...sort, direction: nextDirection } : sort,
    );
  }

  // Add new sort
  return [...currentSort, { key: sortKey, direction: nextDirection }];
}
