/* eslint-disable @typescript-eslint/no-explicit-any */
import { ReactNode } from 'react';

// Sort direction enum
export type SortDirection = 'asc' | 'desc' | null;

// Sort configuration
export interface SortConfig {
  key: string;
  direction: SortDirection;
}

// Column definition interface
export interface ColumnDef<T> {
  id: string;
  header: string | ReactNode;
  accessorKey?: keyof T;
  accessorFn?: (row: T) => void;
  cell?: (props: { row: T; value: any }) => ReactNode;
  sortable?: boolean;
  searchable?: boolean;
  width?: string | number;
  minWidth?: string | number;
  maxWidth?: string | number;
  align?: 'left' | 'center' | 'right';
  className?: string;
  headerClassName?: string;
}

// Pagination configuration
export interface PaginationConfig {
  page: number;
  pageSize: number;
  total: number;
  pageSizeOptions?: number[];
}

// Search and filter configuration
export interface SearchConfig {
  query: string;
  debounceMs?: number;
}

// Table state interface
export interface TableState {
  pagination: PaginationConfig;
  sorting: SortConfig[];
  search: SearchConfig;
  loading: boolean;
  error: string | null;
}

// API response interface for server-side data
export interface TableApiResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
}

// API parameters for server-side requests
export interface TableApiParams {
  page: number;
  pageSize: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  [key: string]: any; // Allow additional filters
}

// Data fetching function type
export type DataFetchFn<T> = (
  params: TableApiParams,
) => Promise<TableApiResponse<T>>;

// Main table props interface (simplified version)
export interface DataTableProps<T> {
  // Required props
  columns: ColumnDef<T>[];
  data: T[];

  // Optional configuration
  initialPageSize?: number;
  pageSizeOptions?: number[];
  enableSearch?: boolean;
  enableSorting?: boolean;
  enablePagination?: boolean;
  multiSort?: boolean;
  searchPlaceholder?: string;
  searchDebounceMs?: number;

  // Styling and layout
  className?: string;
  tableClassName?: string;
  headerClassName?: string;
  bodyClassName?: string;

  // Loading and error states
  loading?: boolean;
  emptyComponent?: ReactNode;

  // Accessibility
  ariaLabel?: string;
  ariaDescription?: string;

  // Additional props
  onRowClick?: (row: T) => void;
  rowClassName?: (row: T) => string;
}

// Legacy interface for backward compatibility (with fetchData)
export interface ServerDataTableProps<T> {
  // Required props
  columns: ColumnDef<T>[];
  fetchData: DataFetchFn<T>;

  // Optional configuration
  initialPageSize?: number;
  pageSizeOptions?: number[];
  enableSearch?: boolean;
  enableSorting?: boolean;
  enablePagination?: boolean;
  multiSort?: boolean;
  searchPlaceholder?: string;
  searchDebounceMs?: number;

  // Styling and layout
  className?: string;
  tableClassName?: string;
  headerClassName?: string;
  bodyClassName?: string;

  // Loading and error states
  loadingComponent?: ReactNode;
  errorComponent?: (error: string) => ReactNode;
  emptyComponent?: ReactNode;

  // Accessibility
  ariaLabel?: string;
  ariaDescription?: string;

  // Additional props
  onRowClick?: (row: T) => void;
  rowClassName?: (row: T) => string;

  // React Query options
  queryKey?: string[];
  refetchOnWindowFocus?: boolean;
  staleTime?: number;
  cacheTime?: number;
}

// Pagination component props
export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  total: number;
  pageSizeOptions: number[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  className?: string;
}

// Search component props
export interface SearchProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  debounceMs?: number;
  className?: string;
  onClear?: () => void;
}

// Sortable header props
export interface SortableHeaderProps {
  children: ReactNode;
  sortKey: string;
  currentSort: SortConfig[];
  onSort: (key: string) => void;
  sortable?: boolean;
  className?: string;
  align?: 'left' | 'center' | 'right';
}

// Table loading state props
export interface TableLoadingProps {
  columns: number;
  rows?: number;
  className?: string;
}

// Table error state props
export interface TableErrorProps {
  error: string;
  onRetry?: () => void;
  className?: string;
}

// Table empty state props
export interface TableEmptyProps {
  message?: string;
  className?: string;
}
