/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useCallback, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  TableState,
  TableApiParams,
  DataFetchFn,
  SortConfig,
  PaginationConfig,
  SearchConfig,
} from './types';
import { updateSortConfig } from './sortable-header';
import { useDebounce } from './search';

interface UseDataTableOptions<T = any> {
  fetchData: DataFetchFn<T>;
  initialPageSize?: number;
  pageSizeOptions?: number[];
  searchDebounceMs?: number;
  multiSort?: boolean;
  queryKey?: string[];
  refetchOnWindowFocus?: boolean;
  staleTime?: number;
  cacheTime?: number;
}

export function useDataTable<T = any>({
  fetchData,
  initialPageSize = 10,
  pageSizeOptions = [10, 25, 50, 100],
  searchDebounceMs = 300,
  multiSort = false,
  queryKey = ['data-table'],
  refetchOnWindowFocus = false,
  staleTime = 5 * 60 * 1000, // 5 minutes
}: UseDataTableOptions<T>) {
  // Table state
  const [pagination, setPagination] = useState<PaginationConfig>({
    page: 1,
    pageSize: initialPageSize,
    total: 0,
  });

  const [sorting, setSorting] = useState<SortConfig[]>([]);
  const [search, setSearch] = useState<SearchConfig>({
    query: '',
    debounceMs: searchDebounceMs,
  });

  // Debounce search query
  const debouncedSearchQuery = useDebounce(search.query, searchDebounceMs);

  // Build API parameters
  const apiParams = useMemo<TableApiParams>(() => {
    const params: TableApiParams = {
      page: pagination.page,
      pageSize: pagination.pageSize,
    };

    // Add search if present
    if (debouncedSearchQuery.trim()) {
      params.search = debouncedSearchQuery.trim();
    }

    // Add sorting if present
    if (sorting.length > 0) {
      const primarySort = sorting[0];
      params.sortBy = primarySort.key;
      params.sortDirection = primarySort.direction || 'asc';
    }

    return params;
  }, [pagination.page, pagination.pageSize, debouncedSearchQuery, sorting]);

  // React Query for data fetching
  const {
    data: response,
    isLoading,
    isError,
    error,
    refetch,
    isFetching,
  } = useQuery({
    queryKey: [...queryKey, apiParams],
    queryFn: () => fetchData(apiParams),
    refetchOnWindowFocus,
    staleTime,
  });

  // Update pagination total when data changes
  React.useEffect(() => {
    if (response) {
      setPagination((prev) => ({
        ...prev,
        total: response.total,
      }));
    }
  }, [response]);

  // Table state object
  const tableState: TableState = {
    pagination: {
      ...pagination,
      total: response?.total || 0,
      pageSizeOptions,
    },
    sorting,
    search,
    loading: isLoading || isFetching,
    error: isError ? (error as Error)?.message || 'An error occurred' : null,
  };

  // Handlers
  const handlePageChange = useCallback((page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  }, []);

  const handlePageSizeChange = useCallback((pageSize: number) => {
    setPagination((prev) => ({
      ...prev,
      pageSize,
      page: 1, // Reset to first page when changing page size
    }));
  }, []);

  const handleSort = useCallback(
    (sortKey: string) => {
      setSorting((prev) => updateSortConfig(prev, sortKey, multiSort));
      // Reset to first page when sorting changes
      setPagination((prev) => ({ ...prev, page: 1 }));
    },
    [multiSort],
  );

  const handleSearch = useCallback((query: string) => {
    setSearch((prev) => ({ ...prev, query }));
    // Reset to first page when search changes
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, []);

  const handleSearchClear = useCallback(() => {
    setSearch((prev) => ({ ...prev, query: '' }));
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, []);

  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  const handleReset = useCallback(() => {
    setPagination({
      page: 1,
      pageSize: initialPageSize,
      total: 0,
    });
    setSorting([]);
    setSearch({ query: '', debounceMs: searchDebounceMs });
  }, [initialPageSize, searchDebounceMs]);

  // Computed values
  const totalPages = Math.ceil((response?.total || 0) / pagination.pageSize);
  const hasData = response?.data && response.data.length > 0;
  const isEmpty = !isLoading && !hasData;

  return {
    // Data
    data: response?.data || [],

    // State
    tableState,

    // Computed values
    totalPages,
    hasData,
    isEmpty,

    // Handlers
    handlePageChange,
    handlePageSizeChange,
    handleSort,
    handleSearch,
    handleSearchClear,
    handleRefresh,
    handleReset,

    // Query state
    isLoading,
    isFetching,
    isError,
    error,
    refetch,
  };
}
