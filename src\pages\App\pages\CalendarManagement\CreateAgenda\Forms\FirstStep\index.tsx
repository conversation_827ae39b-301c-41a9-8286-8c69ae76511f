import { Controller, useFormContext } from 'react-hook-form';

import { LabelAndValue } from '@/@types/LabelAndValue';
import { Combobox } from '@/components/ui/combobox';
import { Input } from '@/components/ui/input';
import { SelectField } from '@/components/ui/select';
import { useApi } from '@/contexts/api';
import { telefoneMask } from '@/utils/formatters/telefoneMask';
import { useQuery } from '@tanstack/react-query';
import { NewAgendaType } from '../validations';

interface FirstStepProps {
  selectedCompromissos: LabelAndValue[];
  setSelectedCompromissos: React.Dispatch<
    React.SetStateAction<LabelAndValue[]>
  >;
}

export function FirstStep({
  selectedCompromissos,
  setSelectedCompromissos,
}: FirstStepProps) {
  const {
    calendar: { remoteLoadCompromissos, remoteLoadExamsType },
  } = useApi();

  const {
    formState: { errors },
    control,
  } = useFormContext<NewAgendaType>();

  const { data: allCompromissos } = useQuery({
    queryKey: ['allCompromissos'],
    queryFn: () => remoteLoadCompromissos(),
  });

  const { data: allExamsType } = useQuery({
    queryKey: ['allExamsType'],
    queryFn: () => remoteLoadExamsType(),
  });

  return (
    <div className="flex flex-col gap-6">
      <div className="flex gap-8">
        <Controller
          control={control}
          name="nome"
          render={({ field }) => (
            <Input
              id="nome"
              type="text"
              placeholder="Digite o nome da agenda"
              label="Nome da agenda"
              value={field.value}
              onChange={field.onChange}
              errorMessage={errors.nome?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="codigo"
          render={({ field }) => (
            <Input
              id="codigo"
              type="text"
              placeholder="Digite o código"
              label="Código SOC"
              onChange={field.onChange}
              value={field.value}
              errorMessage={errors.codigo?.message}
            />
          )}
        />
      </div>
      <div className="flex gap-8">
        <Controller
          control={control}
          name="exame"
          render={({ field: { onChange, value } }) => (
            <SelectField
              label="Tipo do exame"
              placeholder="Selecione o tipo do exame"
              options={
                allExamsType?.result.map((compromisso) => {
                  return {
                    label: compromisso.nome,
                    value: compromisso.codigoSoc,
                  };
                }) ?? []
              }
              onValueChange={onChange}
              value={value}
              errorMessage={errors.exame?.message}
            />
          )}
        />

        <Controller
          control={control}
          name="compromisso"
          render={({ field }) => (
            <Combobox
              label="Tipo do compromisso"
              options={
                allCompromissos?.result.map((compromisso) => {
                  return {
                    label: compromisso.compromisso,
                    value: compromisso.codigoSoc,
                  };
                }) ?? []
              }
              onChange={field.onChange}
              value={field.value}
              errorMessage={errors.compromisso?.message}
            />
          )}
        />
      </div>
      <div className="flex gap-8">
        <Controller
          control={control}
          name="email"
          render={({ field }) => (
            <Input
              id="email"
              type="text"
              placeholder="Digite o email"
              label="Email"
              value={field.value}
              onChange={field.onChange}
              errorMessage={errors.email?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="telefone"
          render={({ field }) => (
            <Input
              id="telefone"
              type="text"
              placeholder="Digite o telefone"
              label="Telefone"
              value={telefoneMask(field.value) ?? ''}
              onChange={field.onChange}
              errorMessage={errors.telefone?.message}
            />
          )}
        />
      </div>
      <Controller
        control={control}
        name="endereco"
        render={({ field }) => (
          <Input
            id="endereco"
            type="text"
            placeholder="Digite o telefone"
            label="Endereço"
            value={field.value}
            onChange={field.onChange}
            errorMessage={errors.endereco?.message}
          />
        )}
      />
    </div>
  );
}
