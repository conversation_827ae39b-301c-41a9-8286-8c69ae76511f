import { useToast } from '@onyma-ds/react';
import { <PERSON><PERSON><PERSON>ider, SubmitHandler, useForm } from 'react-hook-form';
import { NewAgendaSchema, NewAgendaType } from './validations';

import { LabelAndValue } from '@/@types/LabelAndValue';
import { useApi } from '@/contexts/api';
import useDismissModal from '@/hooks/useDismissModal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { FirstStep } from './FirstStep';
import { SecondStep } from './SecondStep';
import { Schedule, formatSchedule, scheduleArray } from './utils';
import { Button } from '@/components/ui/button';
import { DialogFooter } from '@/components/ui/dialog';

export function NewAgendaForm() {
  const { dismiss } = useDismissModal();
  const [schedule, setSchedule] = useState<Schedule[]>(scheduleArray);
  const [scheduleErrors, setScheduleErrors] = useState<string[]>([]);
  const [selectedCompromissos, setSelectedCompromissos] = useState<
    LabelAndValue[]
  >([]);
  const [step, setStep] = useState(1);
  const queryClient = useQueryClient();
  const toast = useToast();
  const {
    calendar: { remoteCreateAgenda },
  } = useApi();

  const methods = useForm<NewAgendaType>({
    resolver: zodResolver(NewAgendaSchema),
  });

  const { mutate: createAgendaMutation } = useMutation({
    mutationFn: ({
      data,
      availability,
    }: {
      data: NewAgendaType;
      availability: { [key: string]: string[] };
    }) =>
      remoteCreateAgenda({
        nome: data.nome,
        codigo: data.codigo,
        email: data.email,
        telefone: data.telefone,
        endereco: data.endereco,
        disponibilidade: availability,
        tipoCompromisso: selectedCompromissos.map(
          (compromisso) => compromisso.value,
        ),
        tipoExame: data.exame.value,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['allCalendars'] });
      toast.addToast({
        type: 'success',
        title: 'Agenda criada com sucesso',
        description: 'a agenda foi criada com sucesso',
      });
      dismiss();
    },
    onError: () => {
      toast.addToast({
        type: 'error',
        title: 'Erro ao criar agenda',
        description: 'Ocorreu um erro ao criar a agenda',
      });
    },
  });

  const handleSubmitForm: SubmitHandler<NewAgendaType> = (data) => {
    const availability = formatSchedule(schedule);

    if (step === 1 && !methods.formState.errors) {
      return;
    }

    if (step === 2) {
      const cleanedErrorsArray = scheduleErrors.filter((error) => error !== '');
      if (cleanedErrorsArray.length) return;
      createAgendaMutation({ data, availability });
      return;
    }
    setStep(2);
  };

  return (
    <form
      className="flex flex-col gap-6"
      onSubmit={methods.handleSubmit(handleSubmitForm)}
    >
      <FormProvider {...methods}>
        {step === 1 && (
          <FirstStep
            selectedCompromissos={selectedCompromissos}
            setSelectedCompromissos={setSelectedCompromissos}
          />
        )}
        {step === 2 && (
          <SecondStep
            schedule={schedule}
            setSchedule={setSchedule}
            scheduleErrors={scheduleErrors}
            setScheduleErrors={setScheduleErrors}
          />
        )}
        <DialogFooter className="w-full flex mt-8 gap-4">
          {step === 2 && (
            <Button
              variant="outline"
              onClick={() => setStep(1)}
              type="button"
            >
              Voltar
            </Button>
          )}
          <Button
            type="submit"
            variant="default"
            color="white"
          >
            Continuar
          </Button>
        </DialogFooter>
      </FormProvider>
    </form>
  );
}
