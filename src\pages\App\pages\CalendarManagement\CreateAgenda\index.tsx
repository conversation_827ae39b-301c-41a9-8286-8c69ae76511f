import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { NewAgendaForm } from './Forms';
import { PlusIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function NewAgendaModal() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button startIcon={<PlusIcon />}>Nova agenda</Button>
      </DialogTrigger>
      <DialogContent className="p-8 min-w-[750px]">
        <DialogHeader className="flex flex-col items-center mb-8">
          <DialogTitle>
            <span className="text-2xl">Nova agenda</span>
          </DialogTitle>
          <DialogDescription>Preencha os dados abaixo</DialogDescription>
        </DialogHeader>

        <NewAgendaForm />
      </DialogContent>
    </Dialog>
  );
}
