import { Button, useToast } from '@onyma-ds/react';
import { <PERSON><PERSON><PERSON>ider, SubmitHandler, useForm } from 'react-hook-form';
import { EditAgendaSchema, EditAgendaType } from './validations';

import { useApi } from '@/contexts/api';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { CalendarType } from '../../types';
import { FirstStep } from './FirstStep';
import { SecondStep } from './SecondStep';
import * as SC from './styles';
import { Schedule, convertToBackend, converterDisponibilidade } from './utils';
import { LabelAndValue } from '@/@types/LabelAndValue';

type EditAgendaFormProps = {
  onClose: () => void;
  selectedAgenda: CalendarType;
};

export function EditAgendaForm({
  onClose,
  selectedAgenda,
}: EditAgendaFormProps) {
  const [scheduleErrors, setScheduleErrors] = useState<string[]>([]);

  const [schedule, setSchedule] = useState<Schedule[]>(
    converterDisponibilidade(selectedAgenda.disponibilidade),
  );
  const [selectedCompromissos, setSelectedCompromissos] = useState<
    LabelAndValue[]
  >([]);
  const [step, setStep] = useState(1);
  const queryClient = useQueryClient();
  const toast = useToast();
  const {
    calendar: { remoteEditAgenda },
  } = useApi();

  const methods = useForm<EditAgendaType>({
    resolver: zodResolver(EditAgendaSchema),
    defaultValues: selectedAgenda,
  });

  const { mutate: editAgendaMutation } = useMutation({
    mutationFn: ({
      data,
      availability,
    }: {
      data: EditAgendaType;
      availability: { [key: string]: string[] | null };
    }) =>
      remoteEditAgenda({
        id: selectedAgenda.id,
        nome: data.nome,
        codigo: data.codigo,
        email: data.email,
        telefone: data.telefone,
        endereco: data.endereco,
        disponibilidade: availability,
        tipoCompromisso: selectedCompromissos.map(
          (compromisso) => compromisso.value,
        ),
        tipoExame: data.exame.value,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['allCalendars'] });
      toast.addToast({
        type: 'success',
        title: 'Agenda editada com sucesso',
        description: 'a agenda foi editada com sucesso',
      });
      onClose();
    },
    onError: () => {
      toast.addToast({
        type: 'error',
        title: 'Erro ao editar agenda',
        description: 'Ocorreu um erro ao editar a agenda',
      });
    },
  });

  const handleSubmitForm: SubmitHandler<EditAgendaType> = (data) => {
    const availability = convertToBackend(schedule);

    if (step === 1 && !methods.formState.errors) {
      return;
    }

    if (step === 2) {
      const cleanedErrorsArray = scheduleErrors.filter((error) => error !== '');
      if (cleanedErrorsArray.length) return;

      editAgendaMutation({ data, availability });
      return;
    }
    setStep(2);
  };

  return (
    <>
      <SC.Container onSubmit={methods.handleSubmit(handleSubmitForm)}>
        <FormProvider {...methods}>
          {step === 1 && (
            <FirstStep
              defaultValues={selectedAgenda}
              selectedCompromissos={selectedCompromissos}
              setSelectedCompromissos={setSelectedCompromissos}
            />
          )}
          {step === 2 && (
            <SecondStep
              schedule={schedule}
              setSchedule={setSchedule}
              scheduleErrors={scheduleErrors}
              setScheduleErrors={setScheduleErrors}
            />
          )}
          <SC.WrapperButtons>
            {step === 2 && (
              <Button
                buttonType="secondary"
                variant="secondary"
                onClick={() => setStep(1)}
                type="button"
              >
                Voltar
              </Button>
            )}
            <Button
              type="submit"
              variant="secondary"
              color="white"
              disabled={
                scheduleErrors.filter((error) => error !== '').length > 0
              }
            >
              Continuar
            </Button>
          </SC.WrapperButtons>
        </FormProvider>
      </SC.Container>
    </>
  );
}
