import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@onyma-ds/react';
import { useApi } from '@/contexts/api';
import { CreateCompanyData, UpdateCompanyData } from './schema';

export const useCreateCompany = () => {
  const queryClient = useQueryClient();
  const { addToast } = useToast();
  const { clients } = useApi();

  return useMutation({
    mutationFn: async (data: CreateCompanyData) => {
      return await clients.createClientCompany({
        name: data.name,
        includedBenSaude: data.includedBenSaude,
        codigoSoc: data.codigoSoc,
        logo: data.logo,
      });
    },
    onSuccess: (result) => {
      addToast({
        type: 'success',
        title: result.title,
        description: result.message,
      });
      queryClient.invalidateQueries({ queryKey: ['all-companies'] });
    },
    onError: (error: {
      response: { data: { title: string; message: string } };
    }) => {
      const errorTitle =
        error.response?.data.title || 'Criação de empresa cliente';
      const errorMessage =
        error.response?.data.message || 'Falha ao criar empresa cliente';

      addToast({
        type: 'error',
        title: errorTitle,
        description: errorMessage,
        timeout: 5000,
      });
    },
  });
};

export const useUpdateCompany = () => {
  const queryClient = useQueryClient();
  const { addToast } = useToast();
  const { clients } = useApi();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateCompanyData;
    }) => {
      return await clients.updateClientCompany({
        id,
        name: data.name,
        includedBenSaude: data.includedBenSaude,
        isActive: data.isActive,
        codigoSoc: data.codigoSoc,
        logo: data.logo,
      });
    },
    onSuccess: (result) => {
      addToast({
        type: 'success',
        title: result.title,
        description: result.message,
      });
      // Invalidate and refetch companies list
      queryClient.invalidateQueries({ queryKey: ['all-companies'] });
    },
    onError: (error: {
      response: { data: { title: string; message: string } };
    }) => {
      const errorTitle =
        error.response?.data.title || 'Edição de empresa cliente';
      const errorMessage =
        error.response?.data.message || 'Falha ao editar empresa cliente';

      addToast({
        type: 'error',
        title: errorTitle,
        description: errorMessage,
        timeout: 5000,
      });
    },
  });
};
