import { z } from 'zod';

export const companyFormSchema = z.object({
  name: z
    .string()
    .min(1, 'Campo obrigatório')
    .max(255, 'Nome deve ter no máximo 255 caracteres')
    .trim(),

  codigoSoc: z
    .string()
    .optional()
    .nullable()
    .transform((val) => (val === '' ? null : val)),

  logo: z
    .string()
    .optional()
    .nullable()
    .transform((val) => (val === '' ? null : val))
    .refine((val) => {
      if (!val) return true;
      try {
        new URL(val);
        return true;
      } catch {
        return false;
      }
    }, 'URL inválida para o logo'),

  isActive: z.boolean().default(true),

  includedBenSaude: z.boolean().default(false),

  createdAt: z.date().optional(),
});

export type CompanyFormData = z.infer<typeof companyFormSchema>;

export const createCompanySchema = companyFormSchema.omit({
  createdAt: true,
  isActive: true,
});

export const updateCompanySchema = companyFormSchema.omit({
  createdAt: true,
});

export type CreateCompanyData = z.infer<typeof createCompanySchema>;
export type UpdateCompanyData = z.infer<typeof updateCompanySchema>;
