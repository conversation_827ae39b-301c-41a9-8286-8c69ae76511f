import { Spinner } from '@/components/loaders/spinner';
import { CustomerCompaniesTable } from '@/components/tables/customerCompanies';
import { UnityCompanyTable } from '@/components/tables/unityCompany';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useApi } from '@/contexts/api';
import { useQuery } from '@tanstack/react-query';

export default function CustomerCompanyPage() {
  const {
    clients: { loadClientCompanies, remoteLoadClientUnity },
  } = useApi();

  const { data: allCompanies, isLoading } = useQuery({
    queryKey: ['all-companies'],
    queryFn: () => loadClientCompanies(),
    refetchOnWindowFocus: false,
    placeholderData: (previousData) => previousData,
  });

  const { data: allUnities, isLoading: isLoadingUnities } = useQuery({
    queryKey: ['all-unities'],
    queryFn: () => remoteLoadClientUnity(),
    refetchOnWindowFocus: false,
    placeholderData: (previousData) => previousData,
  });

  if (isLoading || isLoadingUnities) {
    return <Spinner />;
  }

  return (
    <div className="flex flex-wrap gap-8 p-8 px-6">
      <div className="flex flex-col justify-between w-full">
        <Tabs defaultValue="main-companies">
          <div className="flex justify-between w-full">
            <h1 className="text-2xl font-bold">Empresas clientes</h1>
            <TabsList className="w-[400px]">
              <TabsTrigger value="main-companies">
                Empresas Principais
              </TabsTrigger>
              <TabsTrigger value="unity">Unidades</TabsTrigger>
            </TabsList>
            <div className="w-[250px]" />
          </div>
          <TabsContent
            value="main-companies"
            className="w-full"
          >
            <CustomerCompaniesTable data={allCompanies?.result || []} />
          </TabsContent>
          <TabsContent value="unity">
            <UnityCompanyTable data={allUnities?.result || []} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
