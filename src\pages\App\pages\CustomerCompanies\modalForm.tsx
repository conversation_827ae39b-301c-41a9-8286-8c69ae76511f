import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Plus, SquarePen } from 'lucide-react';
import { ClientCompany } from '@/services/api/clients/remoteLoadClientCompanies';
import CompanyForm from './CompanyForm';

type ModalCompanyFormProps = {
  isEdit?: boolean;
  defaultValues?: ClientCompany;
};

export function ModalCompanyForm({
  isEdit,
  defaultValues,
}: ModalCompanyFormProps) {
  return (
    <Dialog>
      <form>
        <DialogTrigger asChild>
          {isEdit ? (
            <Button
              size="sm"
              variant="default"
              className="w-6 h-6"
              startIcon={<SquarePen />}
            ></Button>
          ) : (
            <Button
              startIcon={<Plus />}
              className="h-7"
            >
              Nova empresa
            </Button>
          )}
        </DialogTrigger>
        <DialogContent className="max-w-[425px] p-12 space-y-4">
          <DialogHeader className="text-center">
            <DialogTitle className="text-center text-2xl">
              {isEdit ? 'Editar' : 'Nova'} empresa cliente
            </DialogTitle>
          </DialogHeader>
          <CompanyForm
            mode={isEdit ? 'edit' : 'create'}
            company={defaultValues}
          />
        </DialogContent>
      </form>
    </Dialog>
  );
}
