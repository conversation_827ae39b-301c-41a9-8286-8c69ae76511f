import { But<PERSON> } from '@/components/ui/button';
import { DialogClose } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { SelectField } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useApi } from '@/contexts/api';
import { NewUnityBody } from '@/services/api/clients/remoteCreateUnity';
import { ClientUnity } from '@/services/api/clients/remoteLoadAllUnities';
import { UpdateUnityBody } from '@/services/api/clients/remoteUpdateUnity';
import { zodResolver } from '@hookform/resolvers/zod';
import { useToast } from '@onyma-ds/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Controller, useForm } from 'react-hook-form';
import { UnitySchema, UnitySchemaType } from './validations';
import useDismissModal from '@/hooks/useDismissModal';
import { formatarCNPJ } from '@/utils/validations/cnpj';

type UnityFormType = {
  isEdit?: boolean;
  company?: ClientUnity;
};

export function UnityForm({ isEdit, company }: UnityFormType) {
  const queryClient = useQueryClient();
  const { dismiss } = useDismissModal();
  const { addToast } = useToast();
  const {
    clients: { loadClientCompanies, remoteCreateUnity, remoteUpdateUnity },
  } = useApi();

  const {
    formState: { errors },
    register,
    handleSubmit,
    setValue,
    control,
  } = useForm<UnitySchemaType>({
    resolver: zodResolver(UnitySchema),
    defaultValues: company && {
      nome: company.nome,
      cnpj: formatarCNPJ(company.cnpj),
      ativo: company.status,
      codigoSoc: company.idEmpresaSoc ? String(company.idEmpresaSoc) : '',
      empresaPrincipal: {
        label: '',
        value: String(company.idEmpresaSoc),
      },
    },
  });

  const { data: allCompanies, isLoading } = useQuery({
    queryKey: ['all-companies'],
    queryFn: () => loadClientCompanies(),
    retry: 1,
    refetchOnWindowFocus: false,
    placeholderData: (previousData) => previousData,
  });

  const { mutate } = useMutation({
    mutationFn: (params: NewUnityBody) => remoteCreateUnity(params),
    onSuccess: () => {
      addToast({
        type: 'success',
        title: 'Unidade criada com sucesso',
        description: 'A unidade foi criada com sucesso',
      });
    },
    onError: () => {
      addToast({
        type: 'error',
        title: 'Erro ao criar unidade',
        description: 'Ocorreu um erro ao criar a unidade',
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['all-unities'] });
      dismiss();
    },
  });

  const { mutate: updateMutate } = useMutation({
    mutationFn: (params: UpdateUnityBody) => remoteUpdateUnity(params),
    onSuccess: () => {
      addToast({
        type: 'success',
        title: 'Unidade atualizada com sucesso',
        description: 'A unidade foi atualizada com sucesso',
      });
    },
    onError: () => {
      addToast({
        type: 'error',
        title: 'Erro ao atualizar unidade',
        description: 'Ocorreu um erro ao atualizar a unidade',
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['all-unities'] });
      dismiss();
    },
  });

  const onSubmit = (data: UnitySchemaType) => {
    if (isEdit) {
      updateMutate({
        idEmpresaClienteUnidade: String(company?.id),
        idEmpresaCliente: company?.idEmpresaCliente as string,
        cnpj: data.cnpj.replace(/\D/g, ''),
        nome: data.nome,
        tipoEstabelecimento: data.tipoEstabelecimento,
        codigoSoc: data.codigoSoc,
        status: data.ativo,
      });
      return;
    }

    mutate({
      cnpj: data.cnpj.replace(/\D/g, ''),
      nome: data.nome,
      status: data.ativo,
      codigoSoc: data.codigoSoc,
      idEmpresaCliente: data.empresaPrincipal.value,
      tipoEstabelecimento: data.tipoEstabelecimento,
    });
  };

  const orderedCompanies = allCompanies?.result.sort((a, b) => {
    if (a.name < b.name) {
      return -1;
    }
    if (a.name > b.name) {
      return 1;
    }
    return 0;
  });

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="flex flex-col gap-6"
    >
      <Controller
        name="empresaPrincipal"
        control={control}
        render={({ field }) => (
          <SelectField
            disabled={isLoading}
            className="bg-white"
            label="Empresa Principal"
            placeholder="Selecione uma empresa"
            options={
              orderedCompanies?.map((cpny) => {
                return {
                  label: cpny.name,
                  value: cpny.id,
                };
              }) ?? []
            }
            defaultValue={{
              label: '',
              value: company?.idEmpresaCliente ?? '',
            }}
            onValueChange={field.onChange}
            errorMessage={errors.empresaPrincipal?.message}
          />
        )}
      />
      <Input
        label="CNPJ"
        placeholder="Digite o link da imagem da empresa"
        defaultValue={company?.cnpj}
        errorMessage={errors.cnpj?.message}
        maxLength={18}
        {...register('cnpj', {
          onChange: (e) => setValue('cnpj', formatarCNPJ(e.target.value)),
        })}
      />
      <Input
        label="Nome"
        placeholder="Digite o nome"
        defaultValue={company?.nome}
        {...register('nome')}
        errorMessage={errors.nome?.message}
      />
      <Input
        label="Código SOC*"
        placeholder="Digite o código SOC da empresa"
        {...register('codigoSoc')}
        errorMessage={errors.codigoSoc?.message}
      />
      <div className="space-y-8">
        <div className="space-y-4">
          <Label>Tipo de estabelecimento</Label>
          <Controller
            name="tipoEstabelecimento"
            control={control}
            defaultValue={
              (company?.tipoEstabelecimento.toLowerCase() as UnitySchemaType['tipoEstabelecimento']) ??
              'matriz'
            }
            render={({ field }) => (
              <RadioGroup
                defaultValue={
                  company?.tipoEstabelecimento.toLowerCase() ?? 'matriz'
                }
                className="flex gap-6"
                onValueChange={field.onChange}
                value={field.value}
              >
                <div className="flex items-center gap-3">
                  <RadioGroupItem
                    {...field}
                    id="matriz"
                    value="matriz"
                  />
                  <Label
                    htmlFor="matriz"
                    className="cursor-pointer"
                  >
                    Matriz
                  </Label>
                </div>
                <div className="flex items-center gap-3">
                  <RadioGroupItem
                    id="filial"
                    {...field}
                    value="filial"
                  />
                  <Label
                    htmlFor="filial"
                    className="cursor-pointer"
                  >
                    Filial
                  </Label>
                </div>
              </RadioGroup>
            )}
          />
        </div>

        <div className="flex items-center space-x-3 mb-8">
          <Switch
            id="isActive"
            defaultChecked={company?.status ?? true}
            onCheckedChange={(checked) => setValue('ativo', checked)}
          />
          <Label
            className="cursor-pointer"
            htmlFor="isActive"
          >
            Ativo
          </Label>
        </div>
      </div>
      <div className="flex gap-4">
        <DialogClose asChild>
          <Button
            className="flex-1"
            type="button"
            size="lg"
            variant="outline"
          >
            Cancelar
          </Button>
        </DialogClose>
        <Button
          className="flex-1"
          type="submit"
          size="lg"
          color="white"
          isLoading={isLoading}
        >
          {isEdit ? 'Editar' : 'Cadastrar'}
        </Button>
      </div>
    </form>
  );
}
