import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { ClientUnity } from '@/services/api/clients/remoteLoadAllUnities';
import { Plus, SquarePen } from 'lucide-react';
import { UnityForm } from './form';

type ModalCompanyFormProps = {
  isEdit?: boolean;
  defaultValues?: ClientUnity;
};

export function ModalUnityForm({
  isEdit,
  defaultValues,
}: ModalCompanyFormProps) {
  return (
    <Dialog>
      <form>
        <DialogTrigger asChild>
          {isEdit ? (
            <Button
              size="sm"
              variant="default"
              className="w-6 h-6"
              startIcon={<SquarePen />}
            ></Button>
          ) : (
            <Button
              startIcon={<Plus />}
              className="h-7"
            >
              Nova unidade
            </Button>
          )}
        </DialogTrigger>
        <DialogContent className="max-w-[425px] p-12 space-y-4">
          <DialogHeader className="text-center">
            <DialogTitle className="text-center text-2xl">
              {isEdit ? 'Editar' : 'Nova'} filial
            </DialogTitle>
          </DialogHeader>
          <UnityForm
            isEdit={isEdit}
            company={defaultValues}
          />
        </DialogContent>
      </form>
    </Dialog>
  );
}
