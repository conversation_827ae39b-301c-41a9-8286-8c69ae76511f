import { z } from 'zod';

const required_error = 'Campo obrigatório';

export const UnitySchema = z.object({
  empresaPrincipal: z.object(
    {
      label: z.string({ required_error }),
      value: z.string({ required_error }),
    },
    { required_error },
  ),
  cnpj: z.string({ required_error }).min(1, { message: required_error }),
  nome: z
    .string({ required_error })
    .min(1, { message: required_error })
    .max(255, 'Nome deve ter no máximo 255 caracteres')
    .trim(),
  codigoSoc: z.string({ required_error }).min(1, { message: required_error }),
  ativo: z.boolean().default(true),
  tipoEstabelecimento: z.enum(['filial', 'matriz'], {
    required_error,
  }),
});

export type UnitySchemaType = z.infer<typeof UnitySchema>;
