const customersImages = [
  {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    logo: '/imgs/empresas-logo/aggreko.png',
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    logo: '/imgs/empresas-logo/assai.png',
  },
  {
    name: 'Brinks',
    logo: '/imgs/empresas-logo/brinks.png',
  },
  {
    name: 'Elopar',
    logo: '/imgs/empresas-logo/elopar.png',
  },
  {
    name: 'IBM',
    logo: '/imgs/empresas-logo/ibm.png',
  },
  {
    name: '<PERSON> CTEEP',
    logo: '/imgs/empresas-logo/isacteep.png',
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    logo: '/imgs/empresas-logo/mercedesbenz.png',
  },
  {
    name: '<PERSON><PERSON>',
    logo: '/imgs/empresas-logo/petlove.png',
  },
  {
    name: '<PERSON><PERSON>',
    logo: '/imgs/empresas-logo/randstad.png',
  },
  {
    name: '<PERSON><PERSON>',
    logo: '/imgs/empresas-logo/rdSaude.png',
  },
  {
    name: 'Veri<PERSON>',
    logo: '/imgs/empresas-logo/verisure.png',
  },
];

export default function Customers() {
  return (
    <section className="text-center flex flex-col gap-16 items-center justify-center min-h-[514px] px-8 mt-8 sm:mt-0">
      <div className="flex flex-col gap-2 font-semibold text-xl">
        <h2 className="text-primary text-sm">CLIENTES</h2>
        <span className="text-[20px]">
          Grandes empresas que utilizam nossas soluções
        </span>
      </div>
      <section className="flex flex-wrap justify-center items-center gap-8 max-w-[768px]">
        {customersImages.map((customer) => (
          <div>
            <img
              key={customer.name}
              src={customer.logo}
              alt={customer.name}
              width={100}
            />
          </div>
        ))}
      </section>
    </section>
  );
}
