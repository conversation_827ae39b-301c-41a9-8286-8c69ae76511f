import { But<PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Contact, Phone } from 'lucide-react';
import { Link } from 'react-router-dom';
import { toast } from 'sonner';

export default function Footer() {
  const handleCopyEmail = () => {
    navigator.clipboard.writeText('<EMAIL>');
    toast.success('E-mail copiado para a área de transferência');
  };

  return (
    <footer className="bg-[#023D68] flex flex-col min-h-[450px]">
      <div className="flex justify-between flex-col sm:flex-row items-center md:items-start gap-8 w-full text-center max-w-[1080px] m-auto p-8">
        <div>
          <img
            src="/imgs/bencorp-logo-white.png"
            alt="Logo da BenCorp"
            loading="lazy"
            width={105}
          />
        </div>
        <div>
          <ul className="text-[16px] text-white flex flex-col gap-1">
            <li className="text-primary"><PERSON><PERSON></li>
            <Link to="/login">
              <li>Fazer login</li>
            </Link>
            <Link to="/#clientes">
              <li>Clientes</li>
            </Link>
            <Link to="/#solucoes">
              <li>Soluções</li>
            </Link>
          </ul>
        </div>
        <div>
          <ul className="text-[16px] text-white space-y-1">
            <li className="text-primary">Institucional</li>
            <li>Política de privacidade</li>
            <li>Termos de uso</li>
          </ul>
        </div>
        <div className="flex flex-col space-y-4">
          <Link
            to="https://bencorp.com.br/contato-bencorp-comercial/"
            target="_blank"
          >
            <Button
              variant="secondary"
              startIcon={<Phone />}
              className="w-[200px] text-primary"
            >
              Fale com um consultor
            </Button>
          </Link>
          <Button
            onClick={handleCopyEmail}
            variant="secondary"
            startIcon={<Contact />}
            className="w-[200px] text-primary"
          >
            Fale com o suporte
          </Button>
        </div>
      </div>
      <div className="flex flex-col justify-center">
        <div className="px-10">
          <Separator className="bg-white/10" />
        </div>
        <span className="text-white/30 text-sm text-center my-8">
          © 2025 BenCorp. Todos os direitos reservados.
        </span>
      </div>
    </footer>
  );
}
