import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useWindowWidth } from '@/hooks';
import { Menu } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

export default function Header() {
  const { isMobile } = useWindowWidth();

  const [menuOpen, setMenuOpen] = useState(false);

  useEffect(() => {
    setMenuOpen(!isMobile);
  }, [isMobile]);

  return (
    <div className=" m-auto sticky top-0 z-10 bg-[#F2FBFB]">
      <header
        className="flex justify-between items-center sm:py-8 px-8 md:px-16 h-[86px] w-full  m-auto max-w-[1280px]"
        data-menu-state={menuOpen ? 'open' : 'closed'}
      >
        <Link
          to="/"
          title="Navegar para a página inicial"
        >
          <img
            src="/svgs/portal_logo.svg"
            alt="Logo da BenCorp"
            className="h-8 w-full"
          />
        </Link>
        <div className="hidden sm:flex gap-8 md:gap-24 text-[#333333] text-sm">
          <a href="/#clientes">Clientes</a>
          <a href="/#solucoes">Soluções</a>
        </div>

        <div className="hidden sm:flex gap-4">
          <Link
            to="/solicitar-acesso"
            title="Solicitar acesso ao sistema"
          >
            <Button
              type="button"
              variant="outline"
              color="white"
              className="bg-transparent border-primary text-primary hover:text-white hover:bg-primary w-36"
            >
              Solicitar acesso
            </Button>
          </Link>
          <Link
            to="/login"
            title="Fazer login"
          >
            <Button
              className="w-28"
              type="button"
              color="white"
            >
              Entrar
            </Button>
          </Link>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger className="bloack sm:hidden">
            <Button
              variant="ghost"
              size="icon"
              className="text-primary hover:text-white hover:bg-primary"
            >
              <Menu />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="p-2"
            align="end"
          >
            <Link to="/login">
              <DropdownMenuItem className="text-primary">
                Entrar
              </DropdownMenuItem>
            </Link>
            <Link to="/solicitar-acesso">
              <DropdownMenuItem className="text-primary">
                Solicitar Acesso
              </DropdownMenuItem>
            </Link>
            <DropdownMenuSeparator />
            <Link to="/#clientes">
              <DropdownMenuItem>Clientes</DropdownMenuItem>
            </Link>
            <Link to="/#solucoes">
              <DropdownMenuItem>Soluões</DropdownMenuItem>
            </Link>
          </DropdownMenuContent>
        </DropdownMenu>
      </header>
    </div>
  );
}
