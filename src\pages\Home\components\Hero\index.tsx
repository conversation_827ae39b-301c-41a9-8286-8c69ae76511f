import { But<PERSON> } from '@/components/ui/button';
import { <PERSON> } from 'react-router-dom';

export default function Hero() {
  return (
    <section className="flex items-center justify-between w-full gap-16 m-auto min-h-[515px] flex-col lg:flex-row px-4 sm:px-16">
      <article className="flex flex-col gap-4 md:max-w-[500px] lg:max-w-[360px] text-center lg:text-left flex-1">
        <h1 className="font-inter font-semibold text-3xl leading-8 lg:w-[390px] tracking-wide">
          Com o Portal do Cliente Bencorp você tem tudo o que precisa para{' '}
          gerenciar a saúde dos seus colaboradores
        </h1>
        <h4 className="text-[#666666] text-[16px]">
          Acompanhe indicadores, abra chamados e solicite treinamentos de forma
          prática e descomplicada.
        </h4>
        <div className="flex justify-center lg:justify-start gap-4">
          <Link to="/solicitar-acesso">
            <Button
              type="button"
              color="white"
              variant="outline"
              className="w-[160px] bg-transparent border-primary text-primary hover:text-white hover:bg-primary rounded-xl"
            >
              Solicitar acesso
            </Button>
          </Link>
          <Link to="/solicitar-acesso">
            <Button
              className="rounded-xl w-[90px]"
              type="button"
            >
              Entrar
            </Button>
          </Link>
        </div>
      </article>
      <div className="flex justify-start xl:scale-115">
        <img
          src="/imgs/portal-system-svg.svg"
          alt="Imagem de uma previsão do sistema interno deste portal"
        />
      </div>
    </section>
  );
}
