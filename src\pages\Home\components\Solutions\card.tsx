import { Button } from '@/components/ui/button';

type CardProps = {
  icon: React.ReactNode;
  title: string;
  description: string;
};

export function Cards({ title, description, icon }: CardProps) {
  return (
    <div className="flex flex-col justify-start items-start text-start gap-6 shadow-md rounded-sm p-6 w-[320px]">
      <div>
        <Button
          className="cursor-default rounded-xl w-16 h-16 bg-[#27B5BF14] hover:bg-[#27B5BF14]"
          startIcon={icon}
        ></Button>
      </div>

      <div className="flex flex-col gap-3">
        <span className="text-[#333333] font-semibold text-[16px]">
          {title}
        </span>
        <span className="text-[#999999] text-sm">{description}</span>
      </div>
    </div>
  );
}
