import {
  BookHeart,
  CalendarDays,
  ChartNoAxesCombined,
  GraduationCap,
  HousePlus,
  MessageSquareText,
} from 'lucide-react';
import { Cards } from './card';

const cardsInfo = [
  {
    icon: (
      <MessageSquareText
        color="#27B5BF"
        style={{
          width: '32px',
          height: '32px',
        }}
      />
    ),
    title: 'Chamad<PERSON>',
    description: 'Abra e gerencie seus chamados de forma rápida e simples',
  },
  {
    icon: (
      <CalendarDays
        color="#27B5BF"
        style={{
          width: '32px',
          height: '32px',
        }}
      />
    ),
    title: 'Indicadores de campanha',
    description: 'Acompanhe o desempenho das suas campanhas em um só lugar',
  },
  {
    icon: (
      <HousePlus
        color="#27B5BF"
        style={{
          width: '32px',
          height: '32px',
        }}
      />
    ),
    title: 'Indicadores de rede credenciada',
    description:
      'Monitore os indicadores da sua rede credenciada com atualizações mensais',
  },
  {
    icon: (
      <ChartNoAxesCombined
        color="#27B5BF"
        style={{
          width: '32px',
          height: '32px',
        }}
      />
    ),
    title: 'Indicadores de saúde',
    description: 'Tenha acesso fácil e prático aos seus indicadores de saúde',
  },
  {
    icon: (
      <BookHeart
        color="#27B5BF"
        style={{
          width: '32px',
          height: '32px',
        }}
      />
    ),
    title: 'Indicadores situacionais de exames',
    description:
      'Monitore de forma simples a situação dos exames dos seus colaboradores',
  },
  {
    icon: (
      <GraduationCap
        color="#27B5BF"
        style={{
          width: '32px',
          height: '32px',
        }}
      />
    ),
    title: 'Solicitação de treinamentos',
    description: 'Solicite e gerencie treinamentos facilmente no Portal Onyma',
  },
];

export default function Solutions() {
  return (
    <section className="text-center flex flex-col gap-16 items-center justify-center min-h-[614px] px-8 mt-8 sm:mt-0 py-24">
      <div className="flex flex-col gap-2 font-semibold text-xl">
        <h2 className="text-primary text-[14px]">SOLUÇÕES</h2>
        <span className="text-[20px]">
          Veja um pouco do que o Portal do Cliente Bencorp oferece à você
        </span>
      </div>
      <section className="flex flex-wrap gap-8 justify-center">
        {cardsInfo.map((card) => (
          <Cards
            key={card.title}
            title={card.title}
            icon={card.icon}
            description={card.description}
          />
        ))}
      </section>
    </section>
  );
}
