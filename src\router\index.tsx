import { createBrowserRouter } from 'react-router-dom';
import HomePage from '@/pages/Home';
import LoginPage from '@/pages/Login';
import LogoutPage from '@/pages/Logout';
import RequestAccessPage from '@/pages/RequestAccess';
import FirstAccessPage from '@/pages/FirstAccess';
import ResetPasswordPage from '@/pages/ResetPassword';
import ChangePasswordPage from '@/pages/ChangePassword';
import PrivateLayout from '@/components/layouts/privateLayout';
import App from '@/pages/App/pages';
import FaqPage from '@/pages/App/pages/Faq';

export const router = createBrowserRouter([
  {
    path: '/',
    element: <HomePage />,
  },
  {
    path: '/login',
    element: <LoginPage />,
  },
  {
    path: '/logout',
    element: <LogoutPage />,
  },
  {
    path: '/solicitar-acesso',
    element: <RequestAccessPage />,
  },
  {
    path: '/primeiro-acesso',
    element: <FirstAccessPage />,
  },
  {
    path: '/esqueci-minha-senha',
    element: <ResetPasswordPage />,
  },
  {
    path: '/resetar-senha/:token',
    element: <ChangePasswordPage />,
  },
  {
    path: '/app',
    element: <PrivateLayout />,
    children: [
      {
        path: '/app/submenu',
        element: <App.SubmenuPage />,
      },
      {
        path: '/app/home',
        element: <App.HomePage />,
      },
      {
        path: '/app/iframe',
        element: <App.IframePage />,
      },
      {
        path: '/app/termo-de-aceite',
        element: <App.TermoDeAceitePage />,
      },
      {
        path: '/app/apps',
        element: <App.AppsPage />,
      },
      {
        path: '/app/apps/agendamento-proprio',
        element: <App.AgendamentoEloparPage />,
      },
      {
        path: '/app/apps/agendamento-soc',
        element: <App.AgendamentoSOCPage />,
      },
      {
        path: '/app/apps/psicossocial-soc',
        element: <App.PsicossocialSocPage />,
      },
      {
        path: '/app/apps/cancelar-consulta',
        element: <App.CancelarConsultaPage />,
      },
      {
        path: '/app/indicadores',
        element: <App.IndicatorsPage />,
      },
      {
        path: '/app/bi',
        element: <App.BiPage />,
      },
      {
        path: '/app/bi/:biId',
        element: <App.BiIdPage />,
      },
      {
        path: '/app/sistema',
        element: <App.SystemPage />,
      },
      {
        path: '/app/relatorios',
        element: <App.ReportsPage />,
      },
      {
        path: '/app/usuarios',
        element: <App.UsersPage />,
      },
      {
        path: '/app/grupo-de-usuarios',
        element: <App.UserGroupPage />,
      },
      {
        path: '/app/gerenciamento-de-downloads',
        element: <App.DownloadManagement />,
      },
      {
        path: '/app/upload-de-arquivos',
        element: <App.FilesUpload />,
      },
      {
        path: '/app/gestao-de-agendas',
        element: <App.AgendaManagementPage />,
      },
      {
        path: '/app/perfil',
        element: <App.PerfilPage />,
      },
      {
        path: '/app/empresas-clientes',
        element: <App.CustomerCompanyPage />,
      },
      {
        path: '/app/empresas-clientes/:clientCompanyId/operadoras',
        element: <App.ClientCompanyOperatorsPage />,
      },
      {
        path: '/app/menus-sistema',
        element: <App.SystemMenusPage />,
      },
      {
        path: '/app/perfis-usuarios',
        element: <App.UserProfilesPage />,
      },
      {
        path: '/app/perfis-usuarios/:id',
        element: <App.UserProfileIdPage />,
      },
      {
        path: '/app/faq',
        element: <FaqPage />,
      },
    ],
  },
]);
